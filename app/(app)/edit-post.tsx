import { useState, useRef, useCallback, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Keyboard,
} from "react-native";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { X, ImageIcon, Link } from "lucide-react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";

import { useUpdateFeedActivity, useGetUploadUrl } from "@/lib/api/queries";
import { useAppContext } from "@/context/app";

// URL detection regex - matches http/https URLs
const urlRegex = /(https?:\/\/[^\s]+)/g;

// Function to extract URLs from text
const extractUrls = (text: string): string[] => {
  const matches = text.match(urlRegex);
  return matches || [];
};

export default function EditPost() {
  const {
    activityId,
    currentText,
    currentImage,
    feedGroup,
    feedId,
    activityData,
  } = useLocalSearchParams<{
    activityId: string;
    currentText: string;
    currentImage: string;
    feedGroup: string;
    feedId: string;
    activityData: string;
  }>();

  // Parse the activity data
  const activity = activityData ? JSON.parse(activityData) : null;

  const [postText, setPostText] = useState(currentText || "");
  const [selectedImage, setSelectedImage] = useState<string | null>(
    currentImage || null
  );
  const [selectedImageAsset, setSelectedImageAsset] =
    useState<ImagePicker.ImagePickerAsset | null>(null);
  const [imageAspectRatio, setImageAspectRatio] = useState<number>(1);
  const [ogPreview, setOgPreview] = useState<any>(activity?.og || null);
  const [isLoadingOg, setIsLoadingOg] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Ref for text input to control focus
  const textInputRef = useRef<TextInput>(null);

  // Initialize image aspect ratio if there's an existing image
  useEffect(() => {
    if (currentImage && !selectedImageAsset) {
      // For React Native Image component, we need to use a different approach
      // Since we can't easily get dimensions, we'll use a default aspect ratio
      // In a real app, you might want to store image dimensions with the activity
      setImageAspectRatio(16 / 9); // Default to 16:9 aspect ratio
    }
  }, [currentImage, selectedImageAsset]);

  const updateMutation = useUpdateFeedActivity(feedGroup!, feedId!);
  const getUploadUrl = useGetUploadUrl();
  const { streamClient, userId } = useAppContext();

  const showToastMessage = (message: string) => {
    Alert.alert("Success", message);
  };

  // Function to fetch Open Graph data
  const fetchOgData = useCallback(
    async (url: string) => {
      if (!streamClient) return null;

      try {
        setIsLoadingOg(true);
        const ogData = await streamClient.og(url);
        return ogData;
      } catch (error) {
        console.error("Error fetching OG data:", error);
        return null;
      } finally {
        setIsLoadingOg(false);
      }
    },
    [streamClient]
  );

  // Handle text change and detect URLs for OG preview
  const handlePostTextChange = useCallback(
    (text: string) => {
      setPostText(text);
      setHasChanges(text !== currentText || selectedImage !== currentImage);

      // Clear existing OG preview if text is empty
      if (!text.trim()) {
        setOgPreview(null);
        return;
      }

      // Extract URLs from the text
      const urls = extractUrls(text);

      if (urls.length > 0) {
        // Use the first URL found for OG preview
        const firstUrl = urls[0];

        // Validate that the URL looks complete
        try {
          new URL(firstUrl);
          // URL is valid, clear any existing preview if it's a different URL
          if (!ogPreview || ogPreview.url !== firstUrl) {
            setOgPreview(null);
          }
        } catch {
          // Invalid URL, clear preview
          setOgPreview(null);
        }
      } else {
        // No URLs found, clear preview
        setOgPreview(null);
      }
    },
    [ogPreview, currentText, currentImage, selectedImage]
  );

  // Debounced effect to fetch OG data after user stops typing
  useEffect(() => {
    if (!postText.trim()) return;

    const urls = extractUrls(postText);
    if (urls.length === 0) return;

    const firstUrl = urls[0];

    // Validate URL
    try {
      new URL(firstUrl);
    } catch {
      return;
    }

    // Only fetch if it's a different URL than current preview
    if (ogPreview && ogPreview.url === firstUrl) return;

    const timeoutId = setTimeout(async () => {
      const ogData = await fetchOgData(firstUrl);
      if (ogData) {
        setOgPreview(ogData);
        setHasChanges(true);
      }
    }, 1000); // Wait 1 second after user stops typing

    return () => clearTimeout(timeoutId);
  }, [postText, ogPreview, fetchOgData]);

  // Handle scroll to dismiss keyboard (Twitter-like behavior)
  const handleScroll = () => {
    textInputRef.current?.blur();
    Keyboard.dismiss();
  };

  // Image picker functions
  const openImagePicker = async (source: "camera" | "library") => {
    try {
      let result: ImagePicker.ImagePickerResult;

      if (source === "camera") {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Camera permission is required to take photos"
          );
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.8,
        });
      } else {
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Photo library permission is required to select photos"
          );
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.9,
        });
      }

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage(asset.uri);
        setSelectedImageAsset(asset);
        setHasChanges(true);

        // Calculate aspect ratio from the cropped image dimensions
        if (asset.width && asset.height) {
          setImageAspectRatio(asset.width / asset.height);
        }
      }
    } catch (error) {
      console.error("Error opening image picker:", error);
      Alert.alert("Error", "Failed to open image picker");
    }
  };

  const showImagePicker = () => {
    Alert.alert(
      "Select Image",
      "Choose an option",
      [
        { text: "Camera", onPress: () => openImagePicker("camera") },
        { text: "Photo Library", onPress: () => openImagePicker("library") },
        { text: "Cancel", style: "cancel" },
      ],
      { cancelable: true }
    );
  };

  const removeImage = () => {
    setSelectedImage(null);
    setSelectedImageAsset(null);
    setHasChanges(true);
  };

  const removeOgPreview = () => {
    setOgPreview(null);
    setHasChanges(true);
  };

  // Handle updating the post
  const handleUpdatePost = async () => {
    if (!postText.trim()) {
      Alert.alert("Error", "Post text cannot be empty");
      return;
    }

    if (!activity) {
      Alert.alert("Error", "Activity data not found");
      return;
    }

    try {
      let attachment = null;

      // Handle image upload if a new image was selected
      if (selectedImageAsset) {
        const fileName =
          selectedImageAsset.fileName || `image_${Date.now()}.jpg`;
        const contentType = selectedImageAsset.mimeType || "image/jpeg";

        // Get upload URL
        const uploadUrlResponse = await new Promise<{
          cdnUrl: string;
          uploadUrl: string;
        }>((resolve, reject) => {
          getUploadUrl.mutate(
            { contentType, fileName },
            {
              onSuccess: resolve,
              onError: reject,
            }
          );
        });

        // Upload the image
        const response = await fetch(selectedImageAsset.uri);
        const blob = await response.blob();

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": contentType,
          },
          body: blob,
        });

        if (!uploadResponse.ok) {
          throw new Error("Failed to upload image");
        }

        attachment = {
          type: "image",
          image_url: uploadUrlResponse.cdnUrl,
        };
      } else if (selectedImage && selectedImage === currentImage) {
        // Keep existing image - preserve the original attachment structure
        if (activity.attachments?.[0]) {
          attachment = activity.attachments[0];
        } else if (activity.image) {
          attachment = {
            type: "image",
            image_url: activity.image,
          };
        }
      }
      // If selectedImage is null, attachment will remain null (image removed)

      // Prepare the updated activity data
      const actorId =
        typeof activity.actor === "string"
          ? activity.actor
          : activity.actor?.id || `user:${userId}`;

      const updatedActivity = {
        id: activity.id,
        actor: actorId,
        verb: activity.verb || "post",
        object: activity.object || `cohort:${feedId}`,
        target: activity.target,
        foreignId: activity.foreign_id || activity.id,
        time: activity.time || new Date().toISOString(),
        message: postText,
        to: activity.to || [`${feedGroup}:${feedId}`],
        ...(attachment && { attachments: [attachment] }),
        ...(ogPreview && { og: ogPreview }),
      };

      // Update the post
      await updateMutation.mutateAsync({
        activities: [updatedActivity],
      });

      showToastMessage("Post updated successfully!");
      router.back();
    } catch (error) {
      console.error("Error updating post:", error);
      Alert.alert("Error", "Failed to update post. Please try again.");
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        "Discard changes?",
        "Are you sure you want to discard your changes?",
        [
          { text: "Keep editing", style: "cancel" },
          {
            text: "Discard",
            style: "destructive",
            onPress: () => router.back(),
          },
        ]
      );
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
          <X size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Post</Text>
        <TouchableOpacity
          style={[
            styles.postButton,
            (!postText.trim() || updateMutation.isPending || !hasChanges) &&
              styles.postButtonDisabled,
          ]}
          onPress={handleUpdatePost}
          disabled={!postText.trim() || updateMutation.isPending || !hasChanges}
        >
          {updateMutation.isPending ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.postButtonText}>Update</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          {/* Text Input */}
          <TextInput
            ref={textInputRef}
            style={styles.textInput}
            placeholder="What's on your mind?"
            placeholderTextColor="#666"
            multiline
            value={postText}
            onChangeText={handlePostTextChange}
            autoFocus
            textAlignVertical="top"
          />

          {/* Selected Image Preview */}
          {selectedImage && (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: selectedImage }}
                style={[
                  styles.selectedImage,
                  { aspectRatio: imageAspectRatio },
                ]}
                contentFit="cover"
              />
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={removeImage}
              >
                <X size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          )}

          {/* OG Preview */}
          {ogPreview && (
            <View style={styles.ogPreviewContainer}>
              <TouchableOpacity
                style={styles.removeOgButton}
                onPress={removeOgPreview}
              >
                <X size={16} color="#666" />
              </TouchableOpacity>

              {ogPreview.images && ogPreview.images.length > 0 && (
                <Image
                  source={{ uri: ogPreview.images[0].image }}
                  style={styles.ogImage}
                  contentFit="cover"
                />
              )}

              <View style={styles.ogContent}>
                {ogPreview.title && (
                  <Text style={styles.ogTitle} numberOfLines={2}>
                    {ogPreview.title}
                  </Text>
                )}
                {ogPreview.description && (
                  <Text style={styles.ogDescription} numberOfLines={3}>
                    {ogPreview.description}
                  </Text>
                )}
                {ogPreview.url && (
                  <Text style={styles.ogUrl} numberOfLines={1}>
                    {ogPreview.url}
                  </Text>
                )}
              </View>
            </View>
          )}

          {/* Loading OG indicator */}
          {isLoadingOg && (
            <View style={styles.loadingOgContainer}>
              <ActivityIndicator size="small" color="#EF5252" />
              <Text style={styles.loadingOgText}>Loading link preview...</Text>
            </View>
          )}
        </ScrollView>

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={showImagePicker}
          >
            <ImageIcon size={24} color="#EF5252" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
  postButton: {
    backgroundColor: "#EF5252",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 70,
    alignItems: "center",
  },
  postButtonDisabled: {
    backgroundColor: "#666",
  },
  postButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  textInput: {
    fontSize: 18,
    color: "#fff",
    paddingVertical: 16,
    minHeight: 120,
    textAlignVertical: "top",
  },
  imageContainer: {
    position: "relative",
    marginVertical: 12,
    borderRadius: 12,
    overflow: "hidden",
  },
  selectedImage: {
    width: "100%",
    borderRadius: 12,
  },
  removeImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  ogPreviewContainer: {
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 12,
    overflow: "hidden",
    marginVertical: 12,
    position: "relative",
  },
  removeOgButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  ogImage: {
    width: "100%",
    height: 200,
  },
  ogContent: {
    padding: 12,
  },
  ogTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  ogDescription: {
    fontSize: 14,
    color: "#ccc",
    marginBottom: 8,
  },
  ogUrl: {
    fontSize: 12,
    color: "#666",
  },
  loadingOgContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  loadingOgText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#666",
  },
  bottomActions: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: "#333",
  },
  actionButton: {
    padding: 8,
  },
});
